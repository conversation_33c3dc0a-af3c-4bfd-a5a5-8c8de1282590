<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF File Splitter</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            border: 3px dashed #e0e6ed;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #4facfe;
            background: #f8fbff;
        }

        .upload-section.dragover {
            border-color: #00f2fe;
            background: #e6f9ff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4rem;
            color: #cbd5e0;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .file-info {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4facfe;
        }

        .sections-input {
            margin: 30px 0;
        }

        .sections-input label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #2d3748;
        }

        .page-ranges {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .range-input {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .range-input input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .range-input input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .add-range-btn, .remove-range-btn {
            background: #48bb78;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .remove-range-btn {
            background: #f56565;
        }

        .add-range-btn:hover {
            background: #38a169;
            transform: translateY(-1px);
        }

        .remove-range-btn:hover {
            background: #e53e3e;
            transform: translateY(-1px);
        }

        .split-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.2rem;
            font-weight: 600;
            width: 100%;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .split-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .split-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .downloads {
            margin-top: 30px;
        }

        .downloads h3 {
            margin-bottom: 20px;
            color: #2d3748;
            font-size: 1.3rem;
        }

        .download-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f7fafc;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #48bb78;
        }

        .download-btn {
            background: #48bb78;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #38a169;
            transform: translateY(-1px);
        }

        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #f56565;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .help-text {
            font-size: 0.9rem;
            color: #718096;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PDF Splitter</h1>
            <p>Split your PDF files into separate documents with ease</p>
        </div>

        <div class="content">
            <div class="upload-section" id="uploadSection">
                <div class="upload-icon">📄</div>
                <p style="margin-bottom: 20px; font-size: 1.1rem; color: #4a5568;">
                    Drag and drop your PDF file here or click to browse
                </p>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    Choose PDF File
                </button>
                <input type="file" id="fileInput" class="file-input" accept=".pdf" />
                <p class="help-text">Maximum file size: 500MB</p>
            </div>

            <div id="fileInfo" class="file-info" style="display: none;">
                <strong>Selected File:</strong> <span id="fileName"></span><br>
                <strong>Size:</strong> <span id="fileSize"></span><br>
                <strong>Pages:</strong> <span id="pageCount"></span>
            </div>

            <div class="sections-input">
                <label>Define Page Ranges to Split:</label>
                <div class="help-text" style="margin-bottom: 15px;">
                    Enter page ranges (e.g., "1-5" for pages 1 to 5, or "7" for single page 7)
                </div>
                <div id="pageRanges" class="page-ranges">
                    <div class="range-input">
                        <input type="text" placeholder="e.g., 1-5" class="range-field">
                        <button class="add-range-btn" onclick="addRange()">+ Add Range</button>
                    </div>
                </div>
            </div>

            <button id="splitBtn" class="split-btn" onclick="splitPDF()" disabled>
                Split PDF
            </button>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Processing PDF...</p>
            </div>

            <div id="error" class="error" style="display: none;"></div>

            <div id="downloads" class="downloads" style="display: none;">
                <h3>Download Split Files</h3>
                <div id="downloadList"></div>
            </div>
        </div>
    </div>

    <script>
        let originalPDF = null;
        let totalPages = 0;
        const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB

        // File upload handling
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('fileInput');

        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        async function handleFile(file) {
            // Validate file type
            if (file.type !== 'application/pdf') {
                showError('Please select a valid PDF file.');
                return;
            }

            // Validate file size
            if (file.size > MAX_FILE_SIZE) {
                showError('File size exceeds 500MB limit. Please choose a smaller file.');
                return;
            }

            hideError();
            
            try {
                const arrayBuffer = await file.arrayBuffer();
                originalPDF = await PDFLib.PDFDocument.load(arrayBuffer);
                totalPages = originalPDF.getPageCount();

                // Display file info
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = formatFileSize(file.size);
                document.getElementById('pageCount').textContent = totalPages;
                document.getElementById('fileInfo').style.display = 'block';
                document.getElementById('splitBtn').disabled = false;

                // Clear previous downloads
                document.getElementById('downloads').style.display = 'none';
            } catch (error) {
                showError('Error loading PDF file. Please make sure it\'s a valid PDF.');
                console.error(error);
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function addRange() {
            const rangesContainer = document.getElementById('pageRanges');
            const rangeDiv = document.createElement('div');
            rangeDiv.className = 'range-input';
            rangeDiv.innerHTML = `
                <input type="text" placeholder="e.g., 6-10" class="range-field">
                <button class="remove-range-btn" onclick="removeRange(this)">Remove</button>
            `;
            rangesContainer.appendChild(rangeDiv);
        }

        function removeRange(button) {
            button.parentElement.remove();
        }

        function parsePageRange(rangeStr, totalPages) {
            rangeStr = rangeStr.trim();
            if (!rangeStr) return [];

            const pages = [];
            const parts = rangeStr.split(',');

            for (let part of parts) {
                part = part.trim();
                if (part.includes('-')) {
                    const [start, end] = part.split('-').map(num => parseInt(num.trim()));
                    if (isNaN(start) || isNaN(end) || start < 1 || end > totalPages || start > end) {
                        throw new Error(`Invalid range: ${part}`);
                    }
                    for (let i = start; i <= end; i++) {
                        pages.push(i - 1); // Convert to 0-based index
                    }
                } else {
                    const pageNum = parseInt(part);
                    if (isNaN(pageNum) || pageNum < 1 || pageNum > totalPages) {
                        throw new Error(`Invalid page: ${part}`);
                    }
                    pages.push(pageNum - 1); // Convert to 0-based index
                }
            }

            return pages;
        }

        async function splitPDF() {
            if (!originalPDF) {
                showError('Please upload a PDF file first.');
                return;
            }

            const rangeFields = document.querySelectorAll('.range-field');
            const ranges = [];

            // Collect and validate all ranges
            for (let i = 0; i < rangeFields.length; i++) {
                const rangeValue = rangeFields[i].value.trim();
                if (rangeValue) {
                    try {
                        const pages = parsePageRange(rangeValue, totalPages);
                        if (pages.length > 0) {
                            ranges.push({
                                name: `Section_${i + 1}_Pages_${rangeValue.replace(/[^0-9,-]/g, '_')}`,
                                pages: pages,
                                rangeStr: rangeValue
                            });
                        }
                    } catch (error) {
                        showError(`Error in range "${rangeValue}": ${error.message}`);
                        return;
                    }
                }
            }

            if (ranges.length === 0) {
                showError('Please specify at least one page range.');
                return;
            }

            hideError();
            showLoading(true);

            try {
                const splitFiles = [];

                for (const range of ranges) {
                    const newPDF = await PDFLib.PDFDocument.create();
                    
                    // Copy pages to new PDF
                    const copiedPages = await newPDF.copyPages(originalPDF, range.pages);
                    copiedPages.forEach(page => newPDF.addPage(page));

                    // Generate PDF bytes
                    const pdfBytes = await newPDF.save();
                    const blob = new Blob([pdfBytes], { type: 'application/pdf' });

                    splitFiles.push({
                        name: `${range.name}.pdf`,
                        blob: blob,
                        rangeStr: range.rangeStr,
                        pageCount: range.pages.length
                    });
                }

                displayDownloadLinks(splitFiles);
            } catch (error) {
                showError('Error splitting PDF: ' + error.message);
                console.error(error);
            } finally {
                showLoading(false);
            }
        }

        function displayDownloadLinks(files) {
            const downloadList = document.getElementById('downloadList');
            downloadList.innerHTML = '';

            files.forEach((file, index) => {
                const downloadItem = document.createElement('div');
                downloadItem.className = 'download-item';
                downloadItem.innerHTML = `
                    <div>
                        <strong>${file.name}</strong><br>
                        <span style="color: #718096; font-size: 0.9rem;">
                            Pages: ${file.rangeStr} (${file.pageCount} page${file.pageCount !== 1 ? 's' : ''})
                        </span>
                    </div>
                    <button class="download-btn" onclick="downloadFile(${index})">Download</button>
                `;
                downloadList.appendChild(downloadItem);
            });

            // Store files globally for download
            window.splitFiles = files;
            document.getElementById('downloads').style.display = 'block';
        }

        function downloadFile(index) {
            const file = window.splitFiles[index];
            const url = URL.createObjectURL(file.blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = file.name;
            a.click();
            URL.revokeObjectURL(url);
        }

        function showError(message) {
            document.getElementById('error').textContent = message;
            document.getElementById('error').style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('splitBtn').disabled = show;
        }
    </script>
</body>
</html>